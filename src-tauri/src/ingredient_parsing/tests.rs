#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;

    /// Mock ingredient parser for testing without loading actual models
    struct MockIngredientParser;

    impl MockIngredientParser {
        /// Mock parse function that simulates Kalosm parsing behavior
        async fn mock_parse_ingredient(
            ingredient_text: &str,
            section: Option<String>,
        ) -> Result<Option<crate::database::Ingredient>, anyhow::Error> {
            // Skip empty ingredients
            if ingredient_text.trim().is_empty() {
                return Ok(None);
            }

            // Mock parsing logic for common test cases
            let parsed = match ingredient_text {
                "2 cups all-purpose flour" => Some(crate::database::Ingredient {
                    name: "all-purpose flour".to_string(),
                    amount: "2".to_string(),
                    unit: "cup".to_string(),
                    category: None,
                    section,
                }),
                "1 tablespoon olive oil" => Some(crate::database::Ingredient {
                    name: "olive oil".to_string(),
                    amount: "1".to_string(),
                    unit: "tbsp".to_string(),
                    category: None,
                    section,
                }),
                "3 large eggs" => Some(crate::database::Ingredient {
                    name: "large eggs".to_string(),
                    amount: "3".to_string(),
                    unit: "".to_string(),
                    category: None,
                    section,
                }),
                "1/2 teaspoon salt" => Some(crate::database::Ingredient {
                    name: "salt".to_string(),
                    amount: "0.5".to_string(),
                    unit: "tsp".to_string(),
                    category: None,
                    section,
                }),
                "1 (15 oz) can diced tomatoes" => Some(crate::database::Ingredient {
                    name: "15 oz can diced tomatoes".to_string(),
                    amount: "1".to_string(),
                    unit: "can".to_string(),
                    category: None,
                    section,
                }),
                "2-3 medium onions" => Some(crate::database::Ingredient {
                    name: "medium onions".to_string(),
                    amount: "2.5".to_string(),
                    unit: "".to_string(),
                    category: None,
                    section,
                }),
                // Invalid ingredients return None
                "chopped" | "to taste" | "" | "   " => None,
                // Fallback for unknown patterns
                _ => Some(crate::database::Ingredient {
                    name: ingredient_text.to_string(),
                    amount: "1".to_string(),
                    unit: "unit".to_string(),
                    category: None,
                    section,
                }),
            };

            Ok(parsed)
        }
    }

    #[tokio::test]
    async fn test_mock_parse_basic_ingredients() {
        // Test basic ingredient parsing
        let result = MockIngredientParser::mock_parse_ingredient("2 cups all-purpose flour", None).await;
        assert!(result.is_ok());
        let ingredient = result.unwrap().unwrap();
        assert_eq!(ingredient.name, "all-purpose flour");
        assert_eq!(ingredient.amount, "2");
        assert_eq!(ingredient.unit, "cup");
        assert_eq!(ingredient.section, None);
    }

    #[tokio::test]
    async fn test_mock_parse_with_section() {
        let result = MockIngredientParser::mock_parse_ingredient(
            "1 tablespoon olive oil",
            Some("Dressing".to_string()),
        ).await;
        assert!(result.is_ok());
        let ingredient = result.unwrap().unwrap();
        assert_eq!(ingredient.name, "olive oil");
        assert_eq!(ingredient.amount, "1");
        assert_eq!(ingredient.unit, "tbsp");
        assert_eq!(ingredient.section, Some("Dressing".to_string()));
    }

    #[tokio::test]
    async fn test_mock_parse_count_based_ingredients() {
        let result = MockIngredientParser::mock_parse_ingredient("3 large eggs", None).await;
        assert!(result.is_ok());
        let ingredient = result.unwrap().unwrap();
        assert_eq!(ingredient.name, "large eggs");
        assert_eq!(ingredient.amount, "3");
        assert_eq!(ingredient.unit, ""); // Count-based ingredients use empty unit
    }

    #[tokio::test]
    async fn test_mock_parse_fractions() {
        let result = MockIngredientParser::mock_parse_ingredient("1/2 teaspoon salt", None).await;
        assert!(result.is_ok());
        let ingredient = result.unwrap().unwrap();
        assert_eq!(ingredient.name, "salt");
        assert_eq!(ingredient.amount, "0.5");
        assert_eq!(ingredient.unit, "tsp");
    }

    #[tokio::test]
    async fn test_mock_parse_parenthetical_amounts() {
        let result = MockIngredientParser::mock_parse_ingredient("1 (15 oz) can diced tomatoes", None).await;
        assert!(result.is_ok());
        let ingredient = result.unwrap().unwrap();
        assert_eq!(ingredient.name, "15 oz can diced tomatoes");
        assert_eq!(ingredient.amount, "1");
        assert_eq!(ingredient.unit, "can");
    }

    #[tokio::test]
    async fn test_mock_parse_ranges() {
        let result = MockIngredientParser::mock_parse_ingredient("2-3 medium onions", None).await;
        assert!(result.is_ok());
        let ingredient = result.unwrap().unwrap();
        assert_eq!(ingredient.name, "medium onions");
        assert_eq!(ingredient.amount, "2.5"); // Average of range
        assert_eq!(ingredient.unit, "");
    }

    #[tokio::test]
    async fn test_mock_parse_invalid_ingredients() {
        // Test invalid ingredients that should return None
        let invalid_ingredients = vec!["chopped", "to taste", "", "   "];
        
        for invalid in invalid_ingredients {
            let result = MockIngredientParser::mock_parse_ingredient(invalid, None).await;
            assert!(result.is_ok());
            assert!(result.unwrap().is_none(), "Expected None for invalid ingredient: '{}'", invalid);
        }
    }

    #[tokio::test]
    async fn test_mock_parse_edge_cases() {
        // Test various edge cases
        let test_cases = vec![
            ("salt", "salt", "1", "unit"),
            ("butter, melted", "butter, melted", "1", "unit"),
            ("1 cup warm water (110 degrees F)", "1 cup warm water (110 degrees F)", "1", "unit"),
        ];

        for (input, expected_name, expected_amount, expected_unit) in test_cases {
            let result = MockIngredientParser::mock_parse_ingredient(input, None).await;
            assert!(result.is_ok(), "Failed to parse: {}", input);
            let ingredient = result.unwrap().unwrap();
            assert_eq!(ingredient.name, expected_name);
            assert_eq!(ingredient.amount, expected_amount);
            assert_eq!(ingredient.unit, expected_unit);
        }
    }

    #[test]
    fn test_kalosm_ingredient_structure() {
        // Test that KalosmIngredient can be created and serialized
        let ingredient = KalosmIngredient {
            name: "all-purpose flour".to_string(),
            amount: 2.0,
            unit: "cup".to_string(),
            section: None,
        };

        assert_eq!(ingredient.name, "all-purpose flour");
        assert_eq!(ingredient.amount, 2.0);
        assert_eq!(ingredient.unit, "cup");
        assert_eq!(ingredient.section, None);

        // Test with section
        let ingredient_with_section = KalosmIngredient {
            name: "sugar".to_string(),
            amount: 1.5,
            unit: "cup".to_string(),
            section: Some("Cake Layer".to_string()),
        };

        assert_eq!(ingredient_with_section.section, Some("Cake Layer".to_string()));
    }

    #[test]
    fn test_unit_normalization_comprehensive() {
        // Test all unit normalizations
        let test_cases = vec![
            ("cup", "cup"),
            ("cups", "cup"),
            ("tablespoon", "tbsp"),
            ("tablespoons", "tbsp"),
            ("tbsp", "tbsp"),
            ("teaspoon", "tsp"),
            ("teaspoons", "tsp"),
            ("tsp", "tsp"),
            ("pound", "lb"),
            ("pounds", "lb"),
            ("lb", "lb"),
            ("ounce", "oz"),
            ("ounces", "oz"),
            ("oz", "oz"),
            ("gram", "g"),
            ("grams", "g"),
            ("g", "g"),
            ("kilogram", "kg"),
            ("kilograms", "kg"),
            ("kg", "kg"),
            ("milliliter", "ml"),
            ("milliliters", "ml"),
            ("ml", "ml"),
            ("liter", "l"),
            ("liters", "l"),
            ("l", "l"),
            ("can", "can"),
            ("cans", "can"),
            ("package", "package"),
            ("packages", "package"),
            ("jar", "jar"),
            ("jars", "jar"),
            ("bottle", "bottle"),
            ("bottles", "bottle"),
            ("bag", "bag"),
            ("bags", "bag"),
            ("box", "box"),
            ("boxes", "box"),
            ("piece", "piece"),
            ("pieces", "piece"),
            ("slice", "slice"),
            ("slices", "slice"),
            ("clove", "clove"),
            ("cloves", "clove"),
            ("stalk", "stalk"),
            ("stalks", "stalk"),
            ("", ""),
            ("unknown_unit", "unit"),
        ];

        for (input, expected) in test_cases {
            assert_eq!(normalize_unit(input), expected, "Failed for input: {}", input);
        }
    }

    #[test]
    fn test_ingredient_parser_creation() {
        // Test that we can create an ingredient parser instance
        let parser = IngredientParser::new();
        
        // The parser should be created successfully
        // Model loading is tested separately to avoid heavy dependencies in unit tests
        assert!(true); // Parser creation succeeded
    }

    #[test]
    fn test_global_parser_instance() {
        // Test that we can get the global parser instance
        let parser1 = get_ingredient_parser();
        let parser2 = get_ingredient_parser();
        
        // Should return the same instance (singleton pattern)
        assert!(std::ptr::eq(parser1, parser2));
    }
}
