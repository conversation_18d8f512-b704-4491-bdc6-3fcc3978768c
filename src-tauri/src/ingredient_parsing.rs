use anyhow::{Context, Result};
use kalosm::language::*;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{debug, error, info, warn};

/// Kalosm-compatible ingredient structure for structured generation
#[derive(<PERSON>lone, Debug, Parse, Schema, Serialize, Deserialize)]
pub struct KalosmIngredient {
    /// The ingredient name (e.g., "all-purpose flour", "chicken breast")
    pub name: String,

    /// The amount as a decimal number (e.g., 2.5, 0.25, 1.0)
    pub amount: f64,

    /// The unit of measurement (e.g., "cup", "tbsp", "lb", "oz", "unit", "")
    pub unit: String,

    /// Optional section for grouped ingredients (e.g., "Cake Layer", "Frosting")
    pub section: Option<String>,
}

/// Ingredient parsing service using Kalosm for local AI inference
pub struct IngredientParser {
    model: Arc<Mutex<Option<Llama>>>,
}

impl IngredientParser {
    /// Create a new ingredient parser instance
    pub fn new() -> Self {
        Self {
            model: Arc::new(Mutex::new(None)),
        }
    }

    /// Initialize the Kalosm model (lazy loading)
    async fn ensure_model_loaded(&self) -> Result<()> {
        let mut model_guard = self.model.lock().await;

        if model_guard.is_none() {
            info!("Loading Kalosm model for ingredient parsing...");

            // Use a smaller, faster model for ingredient parsing
            let model = Llama::phi_3()
                .await
                .context("Failed to load Phi-3 model for ingredient parsing")?;

            *model_guard = Some(model);

            info!("Kalosm ingredient parser initialized successfully");
        }

        Ok(())
    }

    /// Parse a single ingredient string using Kalosm structured generation
    pub async fn parse_ingredient(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Skip empty or invalid ingredients
        if ingredient_text.trim().is_empty() {
            return Ok(None);
        }

        debug!("Parsing ingredient with Kalosm: '{}'", ingredient_text);

        // Ensure model is loaded
        if let Err(e) = self.ensure_model_loaded().await {
            warn!("Failed to load Kalosm model, falling back to regex parsing: {}", e);
            return self.fallback_parse(ingredient_text, section);
        }

        // Get the model
        let model_guard = self.model.lock().await;
        let model = match model_guard.as_ref() {
            Some(model) => model,
            None => {
                warn!("Kalosm model not available, falling back to regex parsing");
                return self.fallback_parse(ingredient_text, section);
            }
        };

        // Prepare the prompt
        let prompt = if let Some(ref sec) = section {
            format!("Parse this ingredient from the '{}' section into structured data with name, amount, unit, and section: {}", sec, ingredient_text)
        } else {
            format!("Parse this ingredient into structured data with name, amount, unit: {}", ingredient_text)
        };

        // Create a task for structured generation
        let task = model
            .task("You are an expert at parsing ingredient strings into structured data. Parse the ingredient text into name, amount, unit, and optional section. Be precise with measurements and units.")
            .typed::<KalosmIngredient>();

        // Parse with Kalosm
        match task(&prompt).await {
            Ok(parsed) => {
                debug!("Successfully parsed ingredient: {:?}", parsed);

                // Convert to database format
                let db_ingredient = crate::database::Ingredient {
                    name: parsed.name,
                    amount: parsed.amount.to_string(),
                    unit: normalize_unit(&parsed.unit),
                    category: None,
                    section: section.or(parsed.section),
                };

                Ok(Some(db_ingredient))
            }
            Err(e) => {
                warn!("Kalosm parsing failed for '{}': {}, falling back to regex", ingredient_text, e);
                self.fallback_parse(ingredient_text, section)
            }
        }
    }

    /// Fallback to existing regex-based parsing
    fn fallback_parse(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // For now, return a simple parsed ingredient as fallback
        // TODO: Integrate with existing parsing logic from main.rs
        if ingredient_text.trim().is_empty() {
            return Ok(None);
        }

        // Simple fallback parsing
        let cleaned_name = ingredient_text.trim().to_string();
        Ok(Some(crate::database::Ingredient {
            name: cleaned_name,
            amount: "1".to_string(),
            unit: "unit".to_string(),
            category: None,
            section,
        }))
    }

    /// Parse multiple ingredients in batch
    pub async fn parse_ingredients_batch(
        &self,
        ingredients: &[String],
    ) -> Result<Vec<crate::database::Ingredient>> {
        let mut parsed_ingredients = Vec::new();

        for ingredient_str in ingredients {
            // Check if ingredient has section information (format: [Section Name] ingredient text)
            let (section, ingredient_text) = if let Some(captures) = 
                regex::Regex::new(r"^\[([^\]]+)\]\s*(.+)$")
                    .unwrap()
                    .captures(ingredient_str) 
            {
                (Some(captures[1].to_string()), captures[2].to_string())
            } else {
                (None, ingredient_str.clone())
            };

            if let Ok(Some(ingredient)) = self.parse_ingredient(&ingredient_text, section).await {
                parsed_ingredients.push(ingredient);
            }
        }

        Ok(parsed_ingredients)
    }
}

/// Normalize unit names to standard abbreviations
fn normalize_unit(unit: &str) -> String {
    match unit.to_lowercase().as_str() {
        "cup" | "cups" => "cup".to_string(),
        "tablespoon" | "tablespoons" | "tbsp" => "tbsp".to_string(),
        "teaspoon" | "teaspoons" | "tsp" => "tsp".to_string(),
        "pound" | "pounds" | "lb" => "lb".to_string(),
        "ounce" | "ounces" | "oz" => "oz".to_string(),
        "gram" | "grams" | "g" => "g".to_string(),
        "kilogram" | "kilograms" | "kg" => "kg".to_string(),
        "milliliter" | "milliliters" | "ml" => "ml".to_string(),
        "liter" | "liters" | "l" => "l".to_string(),
        "can" | "cans" => "can".to_string(),
        "package" | "packages" => "package".to_string(),
        "jar" | "jars" => "jar".to_string(),
        "bottle" | "bottles" => "bottle".to_string(),
        "bag" | "bags" => "bag".to_string(),
        "box" | "boxes" => "box".to_string(),
        "piece" | "pieces" => "piece".to_string(),
        "slice" | "slices" => "slice".to_string(),
        "clove" | "cloves" => "clove".to_string(),
        "stalk" | "stalks" => "stalk".to_string(),
        "" => "".to_string(),
        _ => "unit".to_string(),
    }
}

/// Global instance of the ingredient parser
static INGREDIENT_PARSER: std::sync::OnceLock<IngredientParser> = std::sync::OnceLock::new();

/// Get the global ingredient parser instance
pub fn get_ingredient_parser() -> &'static IngredientParser {
    INGREDIENT_PARSER.get_or_init(|| IngredientParser::new())
}

#[cfg(test)]
mod tests;
