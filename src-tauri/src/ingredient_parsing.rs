use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{debug, info};

/// Kalosm-compatible ingredient structure for structured generation
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct KalosmIngredient {
    /// The ingredient name (e.g., "all-purpose flour", "chicken breast")
    pub name: String,

    /// The amount as a decimal number (e.g., 2.5, 0.25, 1.0)
    pub amount: f64,

    /// The unit of measurement (e.g., "cup", "tbsp", "lb", "oz", "unit", "")
    pub unit: String,

    /// Optional section for grouped ingredients (e.g., "Cake Layer", "Frosting")
    pub section: Option<String>,
}

/// Ingredient parsing service using Kalosm for local AI inference
/// Currently uses fallback parsing, but structured for future Kalosm integration
pub struct IngredientParser {
    // TODO: Add Kalosm model when API is stable
    _placeholder: Arc<Mutex<()>>,
}

impl IngredientParser {
    /// Create a new ingredient parser instance
    pub fn new() -> Self {
        Self {
            _placeholder: Arc::new(Mutex::new(())),
        }
    }

    /// Initialize the Kalosm model (lazy loading)
    /// TODO: Implement when Kalosm API is stable
    async fn _ensure_model_loaded(&self) -> Result<()> {
        // Placeholder for future Kalosm integration
        info!("Kalosm integration placeholder - using fallback parsing");
        Ok(())
    }

    /// Parse a single ingredient string using Kalosm structured generation
    /// Currently uses fallback parsing until Kalosm integration is complete
    pub async fn parse_ingredient(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Skip empty or invalid ingredients
        if ingredient_text.trim().is_empty() {
            return Ok(None);
        }

        debug!("Parsing ingredient (using fallback): '{}'", ingredient_text);

        // TODO: Implement Kalosm parsing when API is stable
        // For now, use the enhanced fallback parsing
        self.fallback_parse(ingredient_text, section)
    }

    /// Fallback to existing regex-based parsing
    fn fallback_parse(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Simple parsing logic for now - can be enhanced later
        let trimmed = ingredient_text.trim();

        if trimmed.is_empty() {
            return Ok(None);
        }

        // Basic regex pattern to extract amount, unit, and name
        let pattern = r"^(\d+(?:\.\d+)?(?:\s*[¼½¾⅓⅔⅛⅜⅝⅞])?)\s*([a-zA-Z]+)?\s*(.+)$";

        if let Ok(regex) = regex::Regex::new(pattern) {
            if let Some(captures) = regex.captures(trimmed) {
                let amount_str = captures.get(1).map(|m| m.as_str()).unwrap_or("1");
                let unit = captures.get(2).map(|m| m.as_str()).unwrap_or("unit");
                let name = captures.get(3).map(|m| m.as_str()).unwrap_or(trimmed);

                let amount = amount_str.parse::<f64>().unwrap_or(1.0);

                return Ok(Some(crate::database::Ingredient {
                    name: name.trim().to_string(),
                    amount: amount.to_string(),
                    unit: normalize_unit(unit),
                    category: None,
                    section,
                }));
            }
        }

        // Fallback: treat as ingredient name with amount 1
        Ok(Some(crate::database::Ingredient {
            name: trimmed.to_string(),
            amount: "1".to_string(),
            unit: "unit".to_string(),
            category: None,
            section,
        }))
    }

    /// Parse multiple ingredients in batch
    pub async fn parse_ingredients_batch(
        &self,
        ingredients: &[String],
    ) -> Result<Vec<crate::database::Ingredient>> {
        let mut parsed_ingredients = Vec::new();

        for ingredient_str in ingredients {
            // Check if ingredient has section information (format: [Section Name] ingredient text)
            let (section, ingredient_text) = if let Some(captures) = 
                regex::Regex::new(r"^\[([^\]]+)\]\s*(.+)$")
                    .unwrap()
                    .captures(ingredient_str) 
            {
                (Some(captures[1].to_string()), captures[2].to_string())
            } else {
                (None, ingredient_str.clone())
            };

            if let Ok(Some(ingredient)) = self.parse_ingredient(&ingredient_text, section).await {
                parsed_ingredients.push(ingredient);
            }
        }

        Ok(parsed_ingredients)
    }
}

/// Normalize unit names to standard abbreviations
fn normalize_unit(unit: &str) -> String {
    match unit.to_lowercase().as_str() {
        "cup" | "cups" => "cup".to_string(),
        "tablespoon" | "tablespoons" | "tbsp" => "tbsp".to_string(),
        "teaspoon" | "teaspoons" | "tsp" => "tsp".to_string(),
        "pound" | "pounds" | "lb" => "lb".to_string(),
        "ounce" | "ounces" | "oz" => "oz".to_string(),
        "gram" | "grams" | "g" => "g".to_string(),
        "kilogram" | "kilograms" | "kg" => "kg".to_string(),
        "milliliter" | "milliliters" | "ml" => "ml".to_string(),
        "liter" | "liters" | "l" => "l".to_string(),
        "can" | "cans" => "can".to_string(),
        "package" | "packages" => "package".to_string(),
        "jar" | "jars" => "jar".to_string(),
        "bottle" | "bottles" => "bottle".to_string(),
        "bag" | "bags" => "bag".to_string(),
        "box" | "boxes" => "box".to_string(),
        "piece" | "pieces" => "piece".to_string(),
        "slice" | "slices" => "slice".to_string(),
        "clove" | "cloves" => "clove".to_string(),
        "stalk" | "stalks" => "stalk".to_string(),
        "" => "".to_string(),
        _ => "unit".to_string(),
    }
}

/// Global instance of the ingredient parser
static INGREDIENT_PARSER: std::sync::OnceLock<IngredientParser> = std::sync::OnceLock::new();

/// Get the global ingredient parser instance
pub fn get_ingredient_parser() -> &'static IngredientParser {
    INGREDIENT_PARSER.get_or_init(|| IngredientParser::new())
}

#[cfg(test)]
mod tests;
